//
//  MainTabView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI
import PopupView

struct MainTabView: View {
    @State private var selectedTab = 0
    
    // Tab配置
    private let tabItems = [
        TabItem(iconName: "footIcon", title: "足迹", tag: 0),
        TabItem(iconName: "petIcon", title: "宠物", tag: 1),
        TabItem(iconName: "scanIcon", title: "扫码", tag: 2),
        TabItem(iconName: "chatIcon", title: "聊天", tag: 3),
        TabItem(iconName: "meIcon", title: "我的", tag: 4)
    ]

    var body: some View {
        // 直接返回TabBarContainer，不重复设置背景
        ZStack {
            AngularGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "010101"), location: 0.00),
                    .init(color: Color(hex: "000000"), location: 0.0048),
                    .init(color: Color(hex: "2E4F17"), location: 0.3699),
                    .init(color: Color(hex: "2A370C"), location: 0.5240),
                    .init(color: Color(hex: "010101"), location: 1.0)
                ]),
                center: UnitPoint(x: 0.4281, y: 0.4891),
                angle: Angle(degrees: 189.17)
            )
            .ignoresSafeArea()
            
            CustomTabBarContainer(selectedTab: $selectedTab, tabItems: tabItems) {
                Group {
                    switch selectedTab {
                    case 0:
                        FootprintView()
                    case 1:
                        PetView()
                    case 2:
                        ScanView()
                    case 3:
                        ChatView()
                    case 4:
                        MyInfoView()
                    default:
                        FootprintView()
                    }
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.15), value: selectedTab)
//                .background(Color.clear)
            }
//            .background(Color.clear)
        }
    }
}

#Preview {
    MainTabView()
}
