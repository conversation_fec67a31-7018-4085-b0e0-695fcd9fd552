//
//  PropInteractionViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import Foundation
import SwiftUI

// MARK: - 道具交互视图模型

/// 道具交互视图模型，负责管理道具交互的状态和业务逻辑
@MainActor
class PropInteractionViewModel: ObservableObject {

    // MARK: - Published 属性

    /// 当前用户发送的已读消息数组
    @Published var readSentInteractions: [PropInteraction] = []

    /// 当前用户发送的未读消息数组
    @Published var unreadSentInteractions: [PropInteraction] = []

    /// 当前用户接收的未读消息数组
    @Published var unreadReceivedInteractions: [PropInteraction] = []

    /// 发送统计信息
    @Published var sendingStats: PropInteractionStats?

    /// 是否存在未读消息（用作sheet展示控制）
    @Published var hasUnreadMessages: Bool = false

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 是否显示错误提示
    @Published var showError: Bool = false

    /// 是否显示新消息弹窗提示
    @Published var showNewMessagePopup: Bool = false

    // MARK: - 私有属性

    private let propService: PropServiceProtocol
    private let currentUserId: String
    private var pollingTimer: Timer?
    private var lastUnreadCount: Int = 0

    // MARK: - 初始化

    /// 初始化视图模型
    /// - Parameters:
    ///   - propService: 道具服务实例，默认使用共享实例
    ///   - currentUserId: 当前用户ID
    init(propService: PropServiceProtocol = PropService.shared, currentUserId: String) {
        self.propService = propService
        self.currentUserId = currentUserId

        // 初始化时加载数据
        Task {
            await loadAllData()
            // 启动轮询检查未读消息
            startPollingUnreadMessages()
        }
    }

    // MARK: - 公共方法

    /// 创建道具交互
    /// - Parameters:
    ///   - receiverUserId: 接收者用户ID
    ///   - propId: 道具ID
    ///   - remark: 备注信息（可选）
    func createPropInteraction(receiverUserId: String, propId: Int, remark: String?) async {
        guard !isLoading else { return }

        isLoading = true
        errorMessage = nil
        showError = false

        do {
            let interaction = try await propService.createPropInteraction(
                senderUserId: currentUserId,
                receiverUserId: receiverUserId,
                propId: propId,
                remark: remark
            )

            // 将新创建的交互添加到未读发送列表
            unreadSentInteractions.append(interaction)

            // 更新统计信息
            await loadSendingStats()

            print("✅ 道具交互创建成功: \(interaction.id)")

        } catch {
            handleError(error)
        }

        isLoading = false
    }

    /// 撤回道具交互（删除）
    /// - Parameter interactionId: 交互记录ID
    func deletePropInteraction(interactionId: String) async {
        guard !isLoading else { return }

        isLoading = true
        errorMessage = nil
        showError = false

        do {
            try await propService.deletePropInteraction(
                interactionId: interactionId,
                userId: currentUserId
            )

            // 从未读发送列表中移除
            unreadSentInteractions.removeAll { $0.id == interactionId }

            // 从已读发送列表中移除
            readSentInteractions.removeAll { $0.id == interactionId }

            // 更新统计信息
            await loadSendingStats()

            print("✅ 道具交互撤回成功: \(interactionId)")

        } catch {
            handleError(error)
        }

        isLoading = false
    }

    /// 标记消息为已读
    /// - Parameter interactionId: 交互记录ID
    func markAsRead(interactionId: String) async {
        guard !isLoading else { return }

        isLoading = true
        errorMessage = nil
        showError = false

        do {
            let updatedInteraction = try await propService.updateReadStatus(
                interactionId: interactionId,
                userId: currentUserId,
                isRead: true,
                receivedTime: Date()
            )

            // 从未读接收列表中移除
            unreadReceivedInteractions.removeAll { $0.id == interactionId }

            // 更新未读消息状态
            updateUnreadMessageStatus()

            print("✅ 消息标记为已读: \(interactionId)")

        } catch {
            handleError(error)
        }

        isLoading = false
    }

    /// 刷新所有数据
    func refreshAllData() async {
        await loadAllData()
    }

    /// 刷新未读消息
    func refreshUnreadMessages() async {
        await loadUnreadReceivedInteractions()
    }

    /// 刷新发送统计
    func refreshSendingStats() async {
        await loadSendingStats()
    }

    /// 启动轮询检查未读消息
    func startPollingUnreadMessages() {
        // 停止之前的定时器
        stopPollingUnreadMessages()

        // 创建新的定时器，每30秒检查一次
        pollingTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkForNewMessages()
            }
        }

        print("✅ 开始轮询检查未读消息")
    }

    /// 停止轮询检查未读消息
    func stopPollingUnreadMessages() {
        pollingTimer?.invalidate()
        pollingTimer = nil
        print("⏹️ 停止轮询检查未读消息")
    }

    /// 检查是否有新消息
    private func checkForNewMessages() async {
        do {
            let interactions = try await propService.getUnreadInteractions(userId: currentUserId)
            let currentUnreadCount = interactions.count

            // 如果未读消息数量增加，显示弹窗提示
            if currentUnreadCount > lastUnreadCount && currentUnreadCount > 0 {
                unreadReceivedInteractions = interactions
                updateUnreadMessageStatus()
                showNewMessagePopup = true
                print("🔔 检测到新消息，显示弹窗提示: \(currentUnreadCount) 条")
            }

            lastUnreadCount = currentUnreadCount

        } catch {
            print("❌ 检查新消息失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 私有方法

    /// 加载所有数据
    private func loadAllData() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.loadSendingStats() }
            group.addTask { await self.loadUnreadReceivedInteractions() }
        }
    }

    /// 加载发送统计数据
    private func loadSendingStats() async {
        do {
            let statsData = try await propService.getSendingStats(userId: currentUserId)

            sendingStats = statsData.stats
            readSentInteractions = statsData.readInteractions
            unreadSentInteractions = statsData.unreadInteractions

            print("✅ 发送统计加载成功: 总计 \(statsData.stats.total)")

        } catch {
            handleError(error)
        }
    }

    /// 加载未读接收消息
    private func loadUnreadReceivedInteractions() async {
        do {
            let interactions = try await propService.getUnreadInteractions(userId: currentUserId)

            unreadReceivedInteractions = interactions
            updateUnreadMessageStatus()

            // 更新未读消息计数
            lastUnreadCount = interactions.count

            print("✅ 未读消息加载成功: \(interactions.count) 条")

        } catch {
            handleError(error)
        }
    }

    /// 更新未读消息状态
    private func updateUnreadMessageStatus() {
        hasUnreadMessages = !unreadReceivedInteractions.isEmpty
    }

    /// 处理错误
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        showError = true
        print("❌ 道具交互错误: \(error.localizedDescription)")
    }
}

// MARK: - 计算属性扩展

extension PropInteractionViewModel {

    /// 所有发送的消息（已读 + 未读）
    var allSentInteractions: [PropInteraction] {
        return (readSentInteractions + unreadSentInteractions).sorted { $0.interactionTime > $1.interactionTime }
    }

    /// 未读消息数量
    var unreadMessageCount: Int {
        return unreadReceivedInteractions.count
    }

    /// 发送的未读消息数量
    var unreadSentMessageCount: Int {
        return unreadSentInteractions.count
    }

    /// 是否有发送的消息
    var hasSentMessages: Bool {
        return !allSentInteractions.isEmpty
    }

    /// 最新的未读消息（用于弹窗显示）
    var latestUnreadMessage: PropInteraction? {
        return unreadReceivedInteractions.first
    }
}

// MARK: - 清理资源

extension PropInteractionViewModel {

    /// 清理资源（在视图消失时调用）
    func cleanup() {
        stopPollingUnreadMessages()
    }
}
